# 简化版客户端-服务器系统需求文档

## 项目概述

构建一个简化的客户端-服务器系统：

- **客户端**：运行在 RK3588 设备，包含参数修改 Web 界面，开机自启动并连接服务器
- **服务器**：显示连接的客户端列表，提供每个客户端的 Web 界面链接，发送短信通知
- **配置管理**：通过.env 文件统一管理所有配置，支持环境变量

## 系统架构

### 整体架构

- **客户端**: RK3588 ARM 平台，前后端架构(FastAPI + HTML)，自带参数修改界面
- **服务器**: Ubuntu 22.04 服务器，前后端架构(FastAPI + HTML)，只管理连接显示
- **通信协议**: 简单的 TCP 心跳连接
- **参数修改**: 完全在客户端内部完成，通过 UDP 与下位机通信
- **配置管理**: 使用.env 文件和环境变量，Docker Compose 自动读取

## 详细需求

### 1. 客户端程序

#### 1.1 核心功能

- **开机自启动**: 容器自动启动
- **连接服务器**: 启动后向服务器发送连接信息(设备 ID + IP)
- **Web 参数界面**: 提供本地 Web 界面用于参数修改
- **UDP 通信**: 直接与下位机进行参数读写通信

#### 1.2 技术架构

- **后端**: Python + FastAPI
- **前端**: HTML + JavaScript (参考 Qt 程序界面)
- **容器**: 基于 ROS2 Humble
- **配置**: 通过.env 文件配置端口和地址
- **端口**: Web 界面可配置(默认 7001)，UDP 通信可配置(默认 18001)

#### 1.3 参数通信协议(客户端与下位机)

基于现有 Qt 程序的 UDP 协议：

```
帧头: 0xFF80, 帧类型: 0x002A
请求ID: 100(读取), 101(写入), 102(读取所有)
参数结构: 256个参数名(8字节) + 256个参数值(4字节)
CRC校验: 字节累加和
```

### 2. 服务器程序

#### 2.1 核心功能

- **监听连接**: TCP 服务器等待客户端连接
- **显示客户端**: Web 界面显示所有连接的客户端 IP 和状态
- **生成链接**: 为每个客户端生成参数修改界面的超链接
- **短信通知**: 新客户端连接时发送短信
- **日志记录**: 记录连接日志到文件

#### 2.2 技术架构

- **后端**: Python + FastAPI
- **前端**: HTML + JavaScript
- **数据存储**: 简单的 JSON 文件
- **配置**: 通过.env 文件配置端口和短信服务
- **端口**: Web 界面可配置(默认 8000)，TCP 监听可配置(默认 8888)

### 3. 通信协议(客户端与服务器)

#### 3.1 连接协议

```json
// 客户端发送连接信息
{
    "device_id": "RK3588_001",
    "ip": "***************",
    "web_port": 8000,
    "timestamp": "2024-01-01T12:00:00Z"
}

// 服务器响应
{
    "status": "connected",
    "message": "连接成功"
}
```

#### 3.2 心跳协议

- 客户端每 30 秒发送心跳
- 服务器超过 60 秒未收到心跳则标记为离线

### 4. 配置管理

#### 4.1 环境变量配置

**客户端配置(.env)**:

```bash
WEB_PORT=7001                # Web服务端口
UDP_PORT=18001              # UDP监听端口
SERVER_HOST=*************   # 服务器IP地址
SERVER_PORT=8888            # 服务器TCP端口
DEVICE_ID=RK3588_001        # 设备唯一标识
UDP_TARGET_IP=************  # 下位机IP地址
UDP_TARGET_PORT=8080        # 下位机UDP端口
```

**服务端配置(.env)**:

```bash
WEB_PORT=8000               # Web服务端口
TCP_PORT=8888               # TCP监听端口
SMS_ENABLED=false           # 是否启用短信
SMS_ACCESS_KEY=your_key     # 短信服务密钥
SMS_PHONE_NUMBER=13800138000 # 接收短信号码
```

#### 4.2 配置特点

- **统一管理**: 所有配置集中在.env 文件
- **自动读取**: Docker Compose 自动读取环境变量
- **即时生效**: 修改配置后重启服务即可
- **环境隔离**: 不同环境使用不同的.env 文件

### 5. 部署需求

#### 5.1 客户端部署

- **容器**: Docker 容器，基于 ROS2 Humble
- **自启动**: 容器重启策略设为 always
- **网络**: 桥接模式，可访问局域网
- **配置**: 通过.env 文件配置所有参数

#### 5.2 服务器部署

- **容器**: Docker 容器
- **端口**: 通过.env 文件配置 Web 和 TCP 端口
- **数据**: 日志文件持久化
- **配置**: 通过.env 文件配置短信服务

### 6. 开发计划

#### 阶段 1: 基础框架(1-2 天)

1. 客户端 FastAPI 框架 + 连接服务器功能
2. 服务器 FastAPI 框架 + TCP 监听功能
3. 基本的 Web 界面

#### 阶段 2: 参数功能(2-3 天)

1. 客户端 UDP 参数通信(参考 Qt 代码)
2. 客户端参数修改 Web 界面
3. 服务器客户端列表显示

#### 阶段 3: 容器化部署(1 天)

1. 编写 Dockerfile 和 docker-compose.yml
2. 测试容器部署
3. 配置自启动

#### 阶段 4: 完善功能(1 天)

1. 短信通知功能
2. 日志记录
3. 界面优化

## 验收标准

1. 客户端容器开机自启动并连接服务器
2. 服务器 Web 界面显示客户端列表和状态
3. 点击客户端链接可访问参数修改界面
4. 参数修改功能正常工作(读取/写入)
5. 新客户端连接时发送短信通知
