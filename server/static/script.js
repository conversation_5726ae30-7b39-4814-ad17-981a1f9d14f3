// 客户端管理系统 JavaScript

class ClientManager {
    constructor() {
        this.autoRefreshInterval = null;
        this.previousClients = new Map(); // 存储上一次的客户端状态
        this.audioContext = null;
        this.soundEnabled = true; // 全局声音开关
        this.clientSoundEnabled = new Map(); // 每个客户端的声音开关
        this.init();
    }

    init() {
        this.bindEvents();
        this.initAudio();
        this.loadClients();
        this.startAutoRefresh();
    }

    // 初始化音频上下文
    initAudio() {
        try {
            // 现代浏览器需要用户交互才能播放音频
            const initAudioContext = () => {
                if (!this.audioContext) {
                    try {
                        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    } catch (error) {
                        console.error('音频上下文初始化失败:', error);
                        this.soundEnabled = false;
                    }
                }
            };

            // 监听多种用户交互事件
            ['click', 'touchstart', 'keydown'].forEach(eventType => {
                document.addEventListener(eventType, initAudioContext, { once: true });
            });

        } catch (error) {
            console.warn('音频初始化失败:', error);
            this.soundEnabled = false;
        }
    }

    // 播放提示音 - 使用Web Audio API生成简单的提示音
    playNotificationSound() {
        console.log('尝试播放提示音...');
        console.log('soundEnabled:', this.soundEnabled);
        console.log('audioContext:', this.audioContext);

        if (!this.soundEnabled) {
            return;
        }

        if (!this.audioContext) {
            try {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            } catch (error) {
                console.error('音频上下文初始化失败:', error);
                return;
            }
        }

        try {

            // 创建长时间的提示音：5秒钟的音调变化
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            // 设置波形类型为方波，声音更响亮
            oscillator.type = 'square';

            // 频率变化：从800Hz开始，逐渐变化
            oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
            oscillator.frequency.linearRampToValueAtTime(1200, this.audioContext.currentTime + 1);
            oscillator.frequency.linearRampToValueAtTime(800, this.audioContext.currentTime + 2);
            oscillator.frequency.linearRampToValueAtTime(1000, this.audioContext.currentTime + 3);
            oscillator.frequency.linearRampToValueAtTime(1200, this.audioContext.currentTime + 4);
            oscillator.frequency.linearRampToValueAtTime(800, this.audioContext.currentTime + 5);

            // 最大音量设置
            gainNode.gain.setValueAtTime(0.8, this.audioContext.currentTime); // 最大音量
            gainNode.gain.setValueAtTime(0.8, this.audioContext.currentTime + 4.5); // 保持最大音量
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 5); // 最后淡出

            // 连接音频节点
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            // 播放5秒钟
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 5);

        } catch (error) {
            console.error('播放提示音失败:', error);
        }
    }

    bindEvents() {
        // 刷新按钮
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.loadClients();
        });

        // 清理按钮
        document.getElementById('cleanup-btn').addEventListener('click', () => {
            this.cleanupOfflineClients();
        });

        // 自动刷新开关
        document.getElementById('auto-refresh').addEventListener('change', (e) => {
            if (e.target.checked) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        });

        // 声音提示开关
        document.getElementById('sound-notification').addEventListener('change', (e) => {
            this.soundEnabled = e.target.checked;
            if (this.soundEnabled && !this.audioContext) {
                // 如果启用声音但音频上下文未初始化，尝试初始化
                try {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                } catch (error) {
                    console.warn('音频初始化失败:', error);
                    this.soundEnabled = false;
                    e.target.checked = false;
                }
            }

            // 播放测试音
            if (this.soundEnabled) {
                this.playNotificationSound();
            }
        });

        // 测试声音按钮
        document.getElementById('test-sound-btn').addEventListener('click', () => {
            this.playNotificationSound();
            this.showBrowserNotification('测试通知', '这是客户端上线提示音测试');
        });

        // 请求通知权限
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                console.log('通知权限:', permission);
            });
        }
    }

    async loadClients() {
        try {
            this.showLoading();

            // 同时获取客户端列表和监控状态
            const [clientsResponse, monitoringResponse] = await Promise.all([
                fetch('/api/clients'),
                fetch('/api/monitoring-status')
            ]);

            const clientsResult = await clientsResponse.json();
            const monitoringResult = await monitoringResponse.json();

            if (clientsResult.success) {
                const monitoringTasks = monitoringResult.success ? monitoringResult.data.monitoring_tasks : {};
                this.displayClients(clientsResult.data, monitoringTasks);
                this.updateStats(clientsResult.total, clientsResult.online);
                this.hideError();
            } else {
                throw new Error(clientsResult.message || '获取客户端列表失败');
            }
        } catch (error) {
            console.error('加载客户端列表失败:', error);
            this.showError('加载客户端列表失败: ' + error.message);
        } finally {
            this.hideLoading();
            this.updateLastUpdateTime();
        }
    }

    displayClients(clients, monitoringTasks = {}) {
        // 检测客户端状态变化并播放提示音
        this.checkClientStatusChanges(clients);

        // 渲染桌面端表格
        this.renderDesktopTable(clients, monitoringTasks);
        // 渲染移动端卡片
        this.renderMobileCards(clients, monitoringTasks);

        // 更新客户端状态记录
        this.updateClientStatusRecord(clients);
    }

    // 检测客户端状态变化
    checkClientStatusChanges(clients) {
        clients.forEach(client => {
            const clientId = client.device_id;
            const currentStatus = client.status;
            const previousStatus = this.previousClients.get(clientId);

            // 如果客户端从离线变为在线，检查是否需要播放提示音
            if (previousStatus === 'offline' && currentStatus === 'online') {
                // 检查该客户端是否启用了提示音
                if (this.clientSoundEnabled.get(clientId)) {
                    this.startContinuousSound(clientId);
                } else {
                    // 全局提示音（原有功能）
                    this.playNotificationSound();
                }

                // 显示浏览器通知（如果用户允许）
                this.showBrowserNotification(`客户端上线`, `${clientId} 已连接`);
            }
            // 如果是新客户端且为在线状态，也播放提示音
            else if (previousStatus === undefined && currentStatus === 'online') {
                // 检查该客户端是否启用了提示音
                if (this.clientSoundEnabled.get(clientId)) {
                    this.startContinuousSound(clientId);
                } else {
                    // 全局提示音（原有功能）
                    this.playNotificationSound();
                }

                this.showBrowserNotification(`新客户端连接`, `${clientId} 已连接`);
            }
        });
    }

    // 切换客户端提示音开关
    toggleClientSound(clientId, enabled) {
        this.clientSoundEnabled.set(clientId, enabled);

        // 刷新界面以更新按钮状态
        setTimeout(() => this.loadClients(), 100);
    }

    // 播放单次提示音
    startContinuousSound(clientId) {
        if (!this.soundEnabled || !this.audioContext) {
            return;
        }

        try {
            // 创建音频实例
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.type = 'square';
            const currentTime = this.audioContext.currentTime;

            // 频率变化 - 10秒完整播放
            oscillator.frequency.setValueAtTime(800, currentTime);
            oscillator.frequency.linearRampToValueAtTime(1000, currentTime + 2.5);
            oscillator.frequency.linearRampToValueAtTime(1200, currentTime + 5);
            oscillator.frequency.linearRampToValueAtTime(1000, currentTime + 7.5);
            oscillator.frequency.linearRampToValueAtTime(800, currentTime + 10);

            // 音量控制 - 渐变消失
            gainNode.gain.setValueAtTime(0.7, currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, currentTime + 10);

            // 连接音频节点
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            // 开始播放
            oscillator.start(currentTime);
            oscillator.stop(currentTime + 10);

        } catch (error) {
            console.warn('播放提示音失败:', error);
        }
    }





    // 更新客户端状态记录
    updateClientStatusRecord(clients) {
        // 创建新的状态记录，但保留不在当前列表中的客户端状态
        const newClientStates = new Map();

        // 记录当前客户端状态
        clients.forEach(client => {
            newClientStates.set(client.device_id, client.status);
        });

        // 对于不在当前列表中的客户端，如果之前是在线的，标记为离线
        for (const [clientId, previousStatus] of this.previousClients) {
            if (!newClientStates.has(clientId) && previousStatus === 'online') {
                newClientStates.set(clientId, 'offline');
            }
        }

        // 更新状态记录
        this.previousClients = newClientStates;
    }

    // 显示浏览器通知
    showBrowserNotification(title, body) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                body: body,
                icon: '/static/favicon.ico', // 如果有图标的话
                tag: 'client-online', // 防止重复通知
                requireInteraction: false,
                silent: false // 允许系统声音
            });
        }
    }

    renderDesktopTable(clients, monitoringTasks = {}) {
        const tbody = document.getElementById('clients-tbody');
        const table = document.getElementById('clients-table');
        const noClients = document.getElementById('no-clients');

        if (clients.length === 0) {
            table.style.display = 'none';
            noClients.style.display = 'block';
            return;
        }

        table.style.display = 'table';
        noClients.style.display = 'none';

        tbody.innerHTML = '';

        clients.forEach(client => {
            const row = document.createElement('tr');

            const statusClass = client.status === 'online' ? 'online' : 'offline';
            const webLink = client.web_url ?
                `<a href="${client.web_url}" target="_blank" class="web-link">参数修改</a>` :
                '<span class="web-link disabled">离线</span>';

            // 检查是否有监控任务
            const isMonitoring = monitoringTasks[client.device_id] && monitoringTasks[client.device_id].is_running;

            // 自动设置按钮状态
            let autoSetupBtn;
            if (isMonitoring) {
                // 正在监控中，显示取消按钮
                autoSetupBtn = `<button class="btn btn-warning" onclick="clientManager.cancelWaitClient('${client.device_id}')">取消等待</button>`;
            } else if (client.status === 'online') {
                // 在线，显示设置参数按钮
                autoSetupBtn = `<button class="btn btn-primary" onclick="clientManager.batchAutoSetupClient('${client.device_id}')">设置</button>`;
            } else {
                // 离线，显示等待上线设置按钮
                autoSetupBtn = `<button class="btn btn-secondary" onclick="clientManager.waitAndSetupClient('${client.device_id}')">等待上线设置</button>`;
            }

            // 提示音控件状态
            const clientId = client.device_id;
            const soundEnabled = this.clientSoundEnabled.get(clientId) || false;
            const checkedAttr = soundEnabled ? 'checked' : '';

            const soundControl = `
                <div class="desktop-sound-control">
                    <label class="sound-switch">
                        <input type="checkbox" ${checkedAttr} onchange="clientManager.toggleClientSound('${clientId}', this.checked)">
                        <span class="sound-slider"></span>
                    </label>
                </div>
            `;

            row.innerHTML = `
                <td>${this.escapeHtml(client.device_id)}</td>
                <td>${this.escapeHtml(client.ip)}</td>
                <td><span class="status ${statusClass}">${client.status}${isMonitoring ? ' (监控中)' : ''}</span></td>
                <td>${this.formatDateTime(client.connected_at)}</td>
                <td>${webLink}</td>
                <td>${autoSetupBtn}</td>
                <td>${soundControl}</td>
                <td>
                    <button class="btn btn-danger" onclick="clientManager.removeClient('${client.device_id}')">
                        删除
                    </button>
                </td>
            `;

            tbody.appendChild(row);
        });
    }

    renderMobileCards(clients, monitoringTasks = {}) {
        let mobileContainer = document.getElementById('mobile-client-cards');
        if (!mobileContainer) {
            // 创建移动端卡片容器
            mobileContainer = document.createElement('div');
            mobileContainer.id = 'mobile-client-cards';
            mobileContainer.className = 'mobile-client-cards';
            document.getElementById('clients-container').appendChild(mobileContainer);
        }

        mobileContainer.innerHTML = '';

        if (clients.length === 0) {
            mobileContainer.innerHTML = '<div class="client-card"><div class="client-card-body" style="text-align: center; color: #6c757d;">暂无客户端连接</div></div>';
            return;
        }

        clients.forEach(client => {
            // 状态样式
            const statusClass = client.status === 'online' ? 'online' : 'offline';
            const statusText = client.status === 'online' ? '在线' : '离线';

            // 检查是否有监控任务
            const isMonitoring = monitoringTasks[client.device_id] && monitoringTasks[client.device_id].is_running;
            const displayStatus = isMonitoring ? `${statusText} (监控中)` : statusText;

            // Web链接
            let webLinkHtml;
            if (client.status === 'online' && client.web_url) {
                webLinkHtml = `<a href="${client.web_url}" target="_blank" class="web-link">参数界面</a>`;
            } else {
                webLinkHtml = `<a href="#" class="web-link disabled">参数界面</a>`;
            }

            // 自动设置按钮
            let autoSetupBtnHtml;
            if (isMonitoring) {
                autoSetupBtnHtml = `<button class="btn btn-warning" onclick="clientManager.cancelWaitClient('${client.device_id}')">取消等待</button>`;
            } else if (client.status === 'online') {
                autoSetupBtnHtml = `<button class="btn btn-primary" onclick="clientManager.batchAutoSetupClient('${client.device_id}')">自动设置</button>`;
            } else {
                autoSetupBtnHtml = `<button class="btn btn-secondary" onclick="clientManager.waitAndSetupClient('${client.device_id}')">等待上线设置</button>`;
            }

            // 提示音控件状态
            const clientId = client.device_id;
            const soundEnabled = this.clientSoundEnabled.get(clientId) || false;
            const checkedAttr = soundEnabled ? 'checked' : '';

            const soundControlHtml = `
                <div class="mobile-sound-control">
                    <span class="sound-label">🔊 上线提示音</span>
                    <label class="sound-switch">
                        <input type="checkbox" ${checkedAttr} onchange="clientManager.toggleClientSound('${clientId}', this.checked)">
                        <span class="sound-slider"></span>
                    </label>
                </div>
            `;

            const cardHtml = `
                <div class="client-card">
                    <div class="client-card-header">
                        <div class="client-card-title">${this.escapeHtml(client.device_id)}</div>
                        <div class="client-card-status">
                            <span class="status ${statusClass}">${displayStatus}</span>
                        </div>
                    </div>
                    <div class="client-card-body">
                        <div class="client-card-info">
                            <div class="client-card-info-item">
                                <div class="client-card-info-label">IP地址</div>
                                <div class="client-card-info-value">${this.escapeHtml(client.ip)}</div>
                            </div>
                            <div class="client-card-info-item">
                                <div class="client-card-info-label">连接时间</div>
                                <div class="client-card-info-value">${this.formatDateTime(client.connected_at)}</div>
                            </div>
                        </div>
                        ${soundControlHtml}
                        <div class="client-card-actions">
                            ${webLinkHtml}
                            ${autoSetupBtnHtml}
                            <button class="btn btn-danger" onclick="clientManager.removeClient('${client.device_id}')">删除</button>
                        </div>
                    </div>
                </div>
            `;

            mobileContainer.innerHTML += cardHtml;
        });
    }

    updateStats(total, online) {
        document.getElementById('total-count').textContent = `总数: ${total}`;
        document.getElementById('online-count').textContent = `在线: ${online}`;
        document.getElementById('offline-count').textContent = `离线: ${total - online}`;
    }

    async removeClient(clientId) {
        if (!confirm(`确定要删除客户端 ${clientId} 吗？`)) {
            return;
        }

        try {
            const response = await fetch(`/api/clients/${clientId}`, {
                method: 'DELETE'
            });
            const result = await response.json();

            if (result.success) {
                this.showMessage('客户端删除成功', 'success');
                this.loadClients();
            } else {
                throw new Error(result.message || '删除客户端失败');
            }
        } catch (error) {
            console.error('删除客户端失败:', error);
            this.showMessage('删除客户端失败: ' + error.message, 'error');
        }
    }

    async cleanupOfflineClients() {
        if (!confirm('确定要清理长时间离线的客户端吗？')) {
            return;
        }

        try {
            const response = await fetch('/api/cleanup', {
                method: 'POST'
            });
            const result = await response.json();

            if (result.success) {
                this.showMessage(result.message, 'success');
                this.loadClients();
            } else {
                throw new Error(result.message || '清理客户端失败');
            }
        } catch (error) {
            console.error('清理客户端失败:', error);
            this.showMessage('清理客户端失败: ' + error.message, 'error');
        }
    }

    startAutoRefresh() {
        this.stopAutoRefresh();
        this.autoRefreshInterval = setInterval(() => {
            this.loadClients();
        }, 5000);
    }

    stopAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
    }

    showLoading() {
        document.getElementById('loading').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    showError(message) {
        const errorDiv = document.getElementById('error');
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
    }

    hideError() {
        document.getElementById('error').style.display = 'none';
    }

    showMessage(message, type = 'info') {
        // 创建临时消息提示
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        `;

        if (type === 'success') {
            messageDiv.style.background = '#27ae60';
        } else if (type === 'error') {
            messageDiv.style.background = '#e74c3c';
        } else {
            messageDiv.style.background = '#3498db';
        }

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }

    updateLastUpdateTime() {
        const now = new Date();
        document.getElementById('last-update').textContent = now.toLocaleString();
    }

    formatDateTime(dateString) {
        try {
            const date = new Date(dateString);
            return date.toLocaleString();
        } catch (error) {
            return dateString;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }



    async waitAndSetupClient(clientId) {
        // 等待客户端上线并自动设置参数（使用客户端环境变量配置）
        if (!confirm(`确定要等待客户端 ${clientId} 上线并自动设置参数吗？\n\n将使用客户端的环境变量配置进行参数设置。\n注意：等待时间无限制，可随时手动取消。`)) {
            return;
        }

        try {
            this.showMessage(`正在等待客户端 ${clientId} 上线...`, 'info');

            const response = await fetch(`/api/clients/${clientId}/wait-and-setup`, {
                method: 'POST'
            });
            const result = await response.json();

            if (result.success) {
                this.showMessage(`已开始监控客户端 ${clientId}，上线后将使用环境变量配置自动设置参数。点击"取消等待"可随时停止监控。`, 'success');
                // 刷新客户端列表以显示最新状态
                this.loadClients();
            } else {
                throw new Error(result.message || '启动监控失败');
            }
        } catch (error) {
            console.error('启动客户端监控失败:', error);
            this.showMessage('启动客户端监控失败: ' + error.message, 'error');
        }
    }

    async cancelWaitClient(clientId) {
        // 取消等待客户端上线
        if (!confirm(`确定要取消等待客户端 ${clientId} 上线吗？`)) {
            return;
        }

        try {
            this.showMessage(`正在取消客户端 ${clientId} 的等待监控...`, 'info');

            const response = await fetch(`/api/clients/${clientId}/cancel-wait`, {
                method: 'POST'
            });
            const result = await response.json();

            if (result.success) {
                this.showMessage(`已取消客户端 ${clientId} 的等待监控`, 'success');
                // 刷新客户端列表
                this.loadClients();
            } else {
                this.showMessage(result.message || '取消监控失败', 'warning');
            }
        } catch (error) {
            console.error('取消客户端监控失败:', error);
            this.showMessage('取消客户端监控失败: ' + error.message, 'error');
        }
    }

    async batchAutoSetupClient(clientId) {
        // 设置客户端参数（使用客户端环境变量配置）
        try {
            this.showMessage('正在设置客户端参数...', 'info');

            const response = await fetch(`/api/clients/${clientId}/auto-setup`, {
                method: 'POST'
            });
            const result = await response.json();

            if (result.success) {
                const clientResult = result.data.client_result;
                let message = `客户端 ${clientId} 参数设置成功`;

                if (clientResult && clientResult.results) {
                    message += `\n\n设置结果 (${clientResult.success_count}/${clientResult.total}):`;
                    clientResult.results.forEach(r => {
                        if (r.success) {
                            if (r.changed) {
                                message += `\n✅ ${r.param_name}: ${r.old_value} → ${r.target_value}`;
                                if (r.verified) {
                                    message += ' (已验证)';
                                }
                            } else {
                                message += `\n✅ ${r.param_name}: 已是目标值 ${r.target_value}`;
                            }
                        } else {
                            message += `\n❌ ${r.param_name}: ${r.message}`;
                        }
                    });
                }

                this.showMessage(message, 'success');
            } else {
                throw new Error(result.message || '参数设置失败');
            }
        } catch (error) {
            console.error('自动设置客户端参数失败:', error);
            let errorMessage = '自动设置客户端参数失败: ' + error.message;

            // 提供详细的错误说明和解决建议
            if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
                errorMessage += '\n\n可能的原因:\n1. 客户端未启动或连接断开\n2. 网络连接问题\n3. 客户端Web端口配置错误';
            } else if (error.message.includes('不支持') || error.message.includes('404')) {
                errorMessage += '\n\n可能的原因:\n1. 客户端版本过旧，不支持自动设置功能\n2. 客户端API接口异常';
            } else if (error.message.includes('参数设置失败') || error.message.includes('无法获取参数列表')) {
                errorMessage += '\n\n可能的原因:\n1. 客户端与下位机连接异常\n2. 下位机未响应或离线\n3. UDP通信配置错误\n\n建议:\n1. 检查客户端日志\n2. 确认下位机在线状态\n3. 验证UDP连接配置';
            }

            this.showMessage(errorMessage, 'error');
        }
    }
}

// 初始化客户端管理器
const clientManager = new ClientManager();

// 页面卸载时停止自动刷新
window.addEventListener('beforeunload', () => {
    clientManager.stopAutoRefresh();
});
