/**
 * 服务器配置管理页面JavaScript
 */

class ServerConfigManager {
    constructor() {
        this.initializeElements();
        this.bindEvents();
        this.loadConfig();
    }

    initializeElements() {
        this.form = document.getElementById('config-form');
        this.saveBtn = document.getElementById('save-btn');
        this.resetBtn = document.getElementById('reset-btn');
        this.backBtn = document.getElementById('back-btn');
        this.messageContainer = document.getElementById('message-container');
    }

    bindEvents() {
        this.saveBtn.addEventListener('click', () => this.saveConfig());
        this.resetBtn.addEventListener('click', () => this.resetConfig());
        this.backBtn.addEventListener('click', () => this.goBack());
    }

    async loadConfig() {
        try {
            this.showLoading(true);
            const response = await fetch('/api/config');
            const result = await response.json();

            if (result.success) {
                this.populateForm(result.data);
                this.showMessage('配置加载成功', 'success');
            } else {
                this.showMessage('加载配置失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('加载配置失败:', error);
            this.showMessage('加载配置失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    populateForm(config) {
        // 填充表单字段
        Object.keys(config).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.value = config[key] || '';
            }
        });
    }

    async saveConfig() {
        try {
            this.showLoading(true);

            // 收集表单数据
            const formData = new FormData(this.form);
            const config = {};

            // 获取所有配置字段
            const configFields = [
                'WEB_PORT', 'TCP_PORT', 'SMS_ENABLED', 'SMS_ACCESS_KEY',
                'SMS_SECRET_KEY', 'SMS_PHONE_NUMBER', 'SMS_TEMPLATE_CODE',
                'SMS_SIGN_NAME', 'LOG_LEVEL', 'LOG_MAX_SIZE_MB',
                'LOG_BACKUP_COUNT', 'DATA_DIR', 'CLIENT_DATA_FILE', 'TIMEZONE'
            ];

            configFields.forEach(field => {
                const element = document.getElementById(field);
                if (element) {
                    config[field] = element.value;
                }
            });

            // 验证必填字段
            const errors = this.validateConfig(config);
            if (errors.length > 0) {
                this.showMessage('配置验证失败:\n' + errors.join('\n'), 'error');
                return;
            }

            // 发送保存请求
            const response = await fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('配置保存成功！', 'success');
                // 重新加载配置以确保显示最新值
                setTimeout(() => this.loadConfig(), 1000);
            } else {
                let errorMsg = '保存配置失败: ' + result.message;
                if (result.errors) {
                    errorMsg += '\n详细错误:\n';
                    Object.keys(result.errors).forEach(key => {
                        errorMsg += `${key}: ${result.errors[key]}\n`;
                    });
                }
                this.showMessage(errorMsg, 'error');
            }
        } catch (error) {
            console.error('保存配置失败:', error);
            this.showMessage('保存配置失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async resetConfig() {
        if (!confirm('确定要重置为默认配置吗？这将覆盖当前所有设置。')) {
            return;
        }

        try {
            this.showLoading(true);

            const response = await fetch('/api/config/reset', {
                method: 'POST'
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('配置已重置为默认值', 'success');
                this.populateForm(result.data);
            } else {
                this.showMessage('重置配置失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('重置配置失败:', error);
            this.showMessage('重置配置失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    validateConfig(config) {
        const errors = [];

        // 验证端口号
        ['WEB_PORT', 'TCP_PORT'].forEach(field => {
            const value = parseInt(config[field]);
            if (isNaN(value) || value < 1000 || value > 65535) {
                errors.push(`${field}: 端口号必须在1000-65535之间`);
            }
        });

        // 验证日志配置
        const maxSize = parseFloat(config.LOG_MAX_SIZE_MB);
        if (isNaN(maxSize) || maxSize <= 0 || maxSize > 1000) {
            errors.push('LOG_MAX_SIZE_MB: 最大文件大小必须在0-1000MB之间');
        }

        const backupCount = parseInt(config.LOG_BACKUP_COUNT);
        if (isNaN(backupCount) || backupCount < 0 || backupCount > 100) {
            errors.push('LOG_BACKUP_COUNT: 备份文件数量必须在0-100之间');
        }

        // 验证必填字段
        const requiredFields = ['WEB_PORT', 'TCP_PORT', 'LOG_LEVEL'];
        requiredFields.forEach(field => {
            if (!config[field] || config[field].trim() === '') {
                errors.push(`${field}: 此字段为必填项`);
            }
        });

        return errors;
    }

    showMessage(message, type) {
        this.messageContainer.innerHTML = `
            <div class="message ${type}">
                ${message.replace(/\n/g, '<br>')}
            </div>
        `;

        // 自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                this.messageContainer.innerHTML = '';
            }, 3000);
        }
    }

    showLoading(loading) {
        if (loading) {
            this.form.classList.add('loading');
            this.saveBtn.disabled = true;
            this.resetBtn.disabled = true;
            this.saveBtn.textContent = '保存中...';
        } else {
            this.form.classList.remove('loading');
            this.saveBtn.disabled = false;
            this.resetBtn.disabled = false;
            this.saveBtn.textContent = '保存配置';
        }
    }

    goBack() {
        window.location.href = '/';
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new ServerConfigManager();
});