/* 客户端管理系统样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header h1 {
    color: #2c3e50;
    font-size: 2rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 30px;
}

.stats {
    display: flex;
    gap: 20px;
}

.stats span {
    padding: 8px 16px;
    background: #ecf0f1;
    border-radius: 4px;
    font-weight: bold;
}

.nav-links {
    display: flex;
    gap: 10px;
}

.nav-links .btn {
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
}

/* 控制按钮样式 */
.controls {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-danger {
    background: #e74c3c;
    color: white;
    font-size: 12px;
    padding: 4px 8px;
}

.btn-danger:hover {
    background: #c0392b;
}

.auto-refresh {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.auto-refresh label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    font-size: 14px;
    user-select: none;
    white-space: nowrap;
}

.auto-refresh input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

/* 客户端列表样式 */
.client-list {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
}

th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

tr:hover {
    background: #f8f9fa;
}

/* 状态样式 */
.status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status.online {
    background: #d4edda;
    color: #155724;
}

.status.offline {
    background: #f8d7da;
    color: #721c24;
}

/* 链接样式 */
.web-link {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

.web-link:hover {
    text-decoration: underline;
}

.web-link.disabled {
    color: #95a5a6;
    cursor: not-allowed;
}

/* 加载和错误状态 */
.loading, .error, .no-data {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
}

.error {
    color: #e74c3c;
    background: #fdf2f2;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin: 20px;
}

/* 底部样式 */
footer {
    text-align: center;
    margin-top: 20px;
    color: #7f8c8d;
    font-size: 14px;
}

/* 移动端卡片布局样式 */
.mobile-client-cards {
    display: none; /* 默认隐藏，只在移动端显示 */
}

.client-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 15px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.client-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.client-card-title {
    font-weight: bold;
    font-size: 16px;
    color: #2c3e50;
    flex: 1;
    word-break: break-word;
}

.client-card-status {
    margin-left: 10px;
}

.client-card-body {
    margin-bottom: 12px;
}

.client-card-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 14px;
}

.client-card-info-item {
    display: flex;
    flex-direction: column;
}

.client-card-info-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 2px;
}

.client-card-info-value {
    font-weight: 500;
    color: #495057;
}

.client-card-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.client-card-actions .btn {
    flex: 1;
    min-width: 0;
    font-size: 13px;
    padding: 8px 12px;
    text-align: center;
}

.client-card-actions .web-link {
    flex: 1;
    display: block;
    text-align: center;
    padding: 8px 12px;
    background: #17a2b8;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 13px;
}

.client-card-actions .web-link.disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.client-card-actions .web-link:hover:not(.disabled) {
    background: #138496;
    text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .stats {
        flex-wrap: wrap;
        justify-content: center;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .auto-refresh {
        margin-left: 0;
        text-align: center;
        justify-content: center;
        gap: 10px;
    }

    .auto-refresh label {
        font-size: 13px;
    }

    .auto-refresh button {
        font-size: 11px;
        padding: 3px 6px;
    }

    /* 移动端隐藏表格，显示卡片布局 */
    #clients-table {
        display: none !important;
    }

    .mobile-client-cards {
        display: block !important;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.client-list {
    animation: fadeIn 0.3s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 滑动开关样式 */
.sound-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.sound-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.sound-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.sound-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.sound-switch input:checked + .sound-slider {
    background-color: #28a745;
}

.sound-switch input:checked + .sound-slider:before {
    transform: translateX(26px);
}

.sound-switch.playing .sound-slider {
    background-color: #ffc107;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 桌面端声音控制 */
.desktop-sound-control {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.desktop-sound-control .sound-switch {
    width: 40px;
    height: 20px;
}

.desktop-sound-control .sound-slider:before {
    height: 14px;
    width: 14px;
    left: 3px;
    bottom: 3px;
}

.desktop-sound-control .sound-switch input:checked + .sound-slider:before {
    transform: translateX(20px);
}

/* 移动端声音控制 */
.mobile-sound-control {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    border-top: 1px solid #eee;
    margin-top: 8px;
}

.mobile-sound-control .sound-label {
    font-size: 14px;
    color: #333;
}

/* 停止按钮样式 */
.stop-sound-btn {
    background-color: #ffc107;
    border: none;
    color: #000;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.stop-sound-btn:hover {
    background-color: #e0a800;
}

.mobile-sound-control .stop-sound-btn {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 15px;
}
