<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器配置管理 - 客户端管理系统</title>
    <link rel="stylesheet" href="/static/style.css">
    <style>
        .config-container {
            max-width: 900px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .config-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }

        .config-form {
            display: grid;
            gap: 20px;
        }

        .config-group {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 20px;
            background: #fafafa;
        }

        .config-group h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
        }

        .config-item {
            display: grid;
            grid-template-columns: 200px 1fr;
            gap: 15px;
            align-items: center;
            margin-bottom: 15px;
        }

        .config-item:last-child {
            margin-bottom: 0;
        }

        .config-label {
            font-weight: 500;
            color: #555;
        }

        .config-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .config-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .config-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn:hover {
            opacity: 0.9;
        }

        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="config-container">
        <div class="config-header">
            <h1>服务器配置管理</h1>
            <button id="back-btn" class="btn btn-secondary">返回主页</button>
        </div>

        <div id="message-container"></div>

        <form id="config-form" class="config-form">
            <div class="config-group">
                <h3>Web服务配置</h3>
                <div class="config-item">
                    <label class="config-label">Web端口:</label>
                    <input type="number" id="WEB_PORT" class="config-input" min="1000" max="65535">
                </div>
            </div>

            <div class="config-group">
                <h3>TCP服务配置</h3>
                <div class="config-item">
                    <label class="config-label">TCP监听端口:</label>
                    <input type="number" id="TCP_PORT" class="config-input" min="1000" max="65535">
                </div>
            </div>

            <div class="config-group">
                <h3>短信服务配置</h3>
                <div class="config-item">
                    <label class="config-label">启用短信服务:</label>
                    <select id="SMS_ENABLED" class="config-input">
                        <option value="false">禁用</option>
                        <option value="true">启用</option>
                    </select>
                </div>
                <div class="config-item">
                    <label class="config-label">Access Key:</label>
                    <input type="text" id="SMS_ACCESS_KEY" class="config-input" placeholder="your_access_key">
                </div>
                <div class="config-item">
                    <label class="config-label">Secret Key:</label>
                    <input type="password" id="SMS_SECRET_KEY" class="config-input" placeholder="your_secret_key">
                </div>
                <div class="config-item">
                    <label class="config-label">接收手机号:</label>
                    <input type="text" id="SMS_PHONE_NUMBER" class="config-input" placeholder="13800138000">
                </div>
                <div class="config-item">
                    <label class="config-label">模板代码:</label>
                    <input type="text" id="SMS_TEMPLATE_CODE" class="config-input" placeholder="SMS_123456789">
                </div>
                <div class="config-item">
                    <label class="config-label">签名名称:</label>
                    <input type="text" id="SMS_SIGN_NAME" class="config-input" placeholder="参数设置系统">
                </div>
            </div>

            <div class="config-group">
                <h3>日志配置</h3>
                <div class="config-item">
                    <label class="config-label">日志级别:</label>
                    <select id="LOG_LEVEL" class="config-input">
                        <option value="DEBUG">DEBUG</option>
                        <option value="INFO">INFO</option>
                        <option value="WARNING">WARNING</option>
                        <option value="ERROR">ERROR</option>
                        <option value="CRITICAL">CRITICAL</option>
                    </select>
                </div>
                <div class="config-item">
                    <label class="config-label">最大文件大小(MB):</label>
                    <input type="number" id="LOG_MAX_SIZE_MB" class="config-input" min="1" max="1000" step="0.1">
                </div>
                <div class="config-item">
                    <label class="config-label">备份文件数量:</label>
                    <input type="number" id="LOG_BACKUP_COUNT" class="config-input" min="0" max="100">
                </div>
            </div>

            <div class="config-group">
                <h3>数据存储配置</h3>
                <div class="config-item">
                    <label class="config-label">数据目录:</label>
                    <input type="text" id="DATA_DIR" class="config-input" placeholder="data">
                </div>
                <div class="config-item">
                    <label class="config-label">客户端数据文件:</label>
                    <input type="text" id="CLIENT_DATA_FILE" class="config-input" placeholder="clients.json">
                </div>
            </div>

            <div class="config-group">
                <h3>系统配置</h3>
                <div class="config-item">
                    <label class="config-label">时区:</label>
                    <input type="text" id="TIMEZONE" class="config-input" placeholder="Asia/Shanghai">
                </div>
            </div>

            <div class="config-actions">
                <button type="button" id="reset-btn" class="btn btn-warning">重置默认</button>
                <button type="button" id="save-btn" class="btn btn-primary">保存配置</button>
            </div>
        </form>
    </div>

    <script src="/static/config.js"></script>
</body>
</html>