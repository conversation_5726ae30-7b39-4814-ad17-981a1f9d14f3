#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
import hmac
import hashlib
import base64
import urllib.parse
import requests
import logging
from typing import Optional, List

logger = logging.getLogger(__name__)

class DingTalkService:
    """钉钉机器人通知服务"""
    
    def __init__(self, webhook_url: str, secret: Optional[str] = None):
        """
        初始化钉钉服务
        
        Args:
            webhook_url: 钉钉机器人webhook地址
            secret: 钉钉机器人密钥（可选，用于签名验证）
        """
        self.webhook_url = webhook_url
        self.secret = secret
        
    def _generate_sign(self, timestamp: str) -> str:
        """
        生成钉钉机器人签名
        
        Args:
            timestamp: 时间戳
            
        Returns:
            签名字符串
        """
        if not self.secret:
            return ""
            
        string_to_sign = f"{timestamp}\n{self.secret}"
        hmac_code = hmac.new(
            self.secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return sign
        
    def send_text_message(self, content: str, at_mobiles: Optional[List[str]] = None, at_all: bool = False) -> bool:
        """
        发送文本消息
        
        Args:
            content: 消息内容
            at_mobiles: @的手机号列表
            at_all: 是否@所有人
            
        Returns:
            发送是否成功
        """
        try:
            # 生成时间戳和签名
            timestamp = str(round(time.time() * 1000))
            sign = self._generate_sign(timestamp)
            
            # 构建请求URL
            url = self.webhook_url
            if sign:
                url += f"&timestamp={timestamp}&sign={sign}"
            
            # 构建消息体
            data = {
                "msgtype": "text",
                "text": {
                    "content": content
                }
            }
            
            # 添加@信息
            if at_mobiles or at_all:
                data["at"] = {
                    "atMobiles": at_mobiles or [],
                    "isAtAll": at_all
                }
            
            # 发送请求
            headers = {'Content-Type': 'application/json'}
            response = requests.post(url, json=data, headers=headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    logger.info(f"钉钉消息发送成功: {content[:50]}...")
                    return True
                else:
                    logger.error(f"钉钉消息发送失败: {result.get('errmsg', '未知错误')}")
                    return False
            else:
                logger.error(f"钉钉API请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"发送钉钉消息异常: {e}")
            return False
    
    def send_markdown_message(self, title: str, text: str, at_mobiles: Optional[List[str]] = None, at_all: bool = False) -> bool:
        """
        发送Markdown消息
        
        Args:
            title: 消息标题
            text: Markdown格式的消息内容
            at_mobiles: @的手机号列表
            at_all: 是否@所有人
            
        Returns:
            发送是否成功
        """
        try:
            # 生成时间戳和签名
            timestamp = str(round(time.time() * 1000))
            sign = self._generate_sign(timestamp)
            
            # 构建请求URL
            url = self.webhook_url
            if sign:
                url += f"&timestamp={timestamp}&sign={sign}"
            
            # 构建消息体
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "title": title,
                    "text": text
                }
            }
            
            # 添加@信息
            if at_mobiles or at_all:
                data["at"] = {
                    "atMobiles": at_mobiles or [],
                    "isAtAll": at_all
                }
            
            # 发送请求
            headers = {'Content-Type': 'application/json'}
            response = requests.post(url, json=data, headers=headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    logger.info(f"钉钉Markdown消息发送成功: {title}")
                    return True
                else:
                    logger.error(f"钉钉Markdown消息发送失败: {result.get('errmsg', '未知错误')}")
                    return False
            else:
                logger.error(f"钉钉API请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"发送钉钉Markdown消息异常: {e}")
            return False

# 全局钉钉服务实例
_dingtalk_service = None

def init_dingtalk_service(webhook_url: str, secret: Optional[str] = None):
    """初始化钉钉服务"""
    global _dingtalk_service
    _dingtalk_service = DingTalkService(webhook_url, secret)
    logger.info("钉钉通知服务初始化完成")

def send_client_online_notification(client_id: str, client_ip: str) -> bool:
    """
    发送客户端上线通知
    
    Args:
        client_id: 客户端ID
        client_ip: 客户端IP地址
        
    Returns:
        发送是否成功
    """
    if not _dingtalk_service:
        logger.warning("钉钉服务未初始化")
        return False
    
    # 构建消息内容
    title = "🟢 设备上线通知"
    content = f"""## {title}

**设备ID:** {client_id}  
**IP地址:** {client_ip}  
**时间:** {time.strftime('%Y-%m-%d %H:%M:%S')}  

设备已成功连接到参数管理系统！"""
    
    return _dingtalk_service.send_markdown_message(title, content)

def send_client_offline_notification(client_id: str, client_ip: str) -> bool:
    """
    发送客户端离线通知
    
    Args:
        client_id: 客户端ID
        client_ip: 客户端IP地址
        
    Returns:
        发送是否成功
    """
    if not _dingtalk_service:
        logger.warning("钉钉服务未初始化")
        return False
    
    # 构建消息内容
    title = "🔴 设备离线通知"
    content = f"""## {title}

**设备ID:** {client_id}  
**IP地址:** {client_ip}  
**时间:** {time.strftime('%Y-%m-%d %H:%M:%S')}  

设备已从参数管理系统断开连接！"""
    
    return _dingtalk_service.send_markdown_message(title, content)

def send_test_notification() -> bool:
    """发送测试通知"""
    if not _dingtalk_service:
        logger.warning("钉钉服务未初始化")
        return False
    
    title = "🧪 钉钉通知测试"
    content = f"""## {title}

这是一条测试消息，用于验证钉钉机器人通知功能是否正常工作。

**测试时间:** {time.strftime('%Y-%m-%d %H:%M:%S')}  
**系统:** 参数管理系统  

如果您收到此消息，说明钉钉通知配置成功！✅"""
    
    return _dingtalk_service.send_markdown_message(title, content)
