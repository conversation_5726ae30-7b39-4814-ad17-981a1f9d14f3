#!/usr/bin/env python3
"""
Web服务器
提供客户端管理的Web界面和API，集成日志管理和配置管理
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os
import logging
import asyncio
import aiohttp
from datetime import datetime

from client_manager import ClientManager
from log_manager import get_log_manager
from config_manager import get_config_manager

# 创建FastAPI应用
app = FastAPI(title="客户端管理系统", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
static_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")
app.mount("/static", StaticFiles(directory=static_path), name="static")

# 全局变量
client_manager = None
global_config_manager = None
global_log_manager = None

@app.get("/")
async def index():
    """主页"""
    index_file = os.path.join(static_path, "index.html")
    if os.path.exists(index_file):
        return FileResponse(index_file)
    else:
        return {"message": "客户端管理系统", "status": "running"}

@app.get("/api/clients")
async def get_clients():
    """获取所有客户端信息"""
    try:
        clients = client_manager.get_all_clients()
        return {
            "success": True,
            "data": clients,
            "total": len(clients),
            "online": sum(1 for c in clients if c['status'] == 'online')
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取客户端列表失败: {str(e)}")

@app.get("/api/clients/{client_id}")
async def get_client(client_id: str):
    """获取单个客户端信息"""
    try:
        client = client_manager.get_client(client_id)
        if client:
            return {"success": True, "data": client}
        else:
            raise HTTPException(status_code=404, detail="客户端不存在")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取客户端信息失败: {str(e)}")

@app.delete("/api/clients/{client_id}")
async def remove_client(client_id: str):
    """移除客户端"""
    try:
        success = client_manager.remove_client(client_id)
        if success:
            return {"success": True, "message": "客户端已移除"}
        else:
            raise HTTPException(status_code=404, detail="客户端不存在")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"移除客户端失败: {str(e)}")

@app.get("/api/stats")
async def get_stats():
    """获取系统统计信息"""
    try:
        clients = client_manager.get_all_clients()
        online_count = sum(1 for c in clients if c['status'] == 'online')
        offline_count = len(clients) - online_count

        return {
            "success": True,
            "data": {
                "total_clients": len(clients),
                "online_clients": online_count,
                "offline_clients": offline_count,
                "last_updated": client_manager.get_all_clients()[0]['connected_at'] if clients else None
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@app.post("/api/cleanup")
async def cleanup_offline_clients(days: int = 0):
    """清理离线的客户端"""
    try:
        removed_count = client_manager.cleanup_offline_clients(days)
        if days == 0:
            message = f"清理了 {removed_count} 个离线客户端"
        else:
            message = f"清理了 {removed_count} 个长时间离线的客户端"

        return {
            "success": True,
            "message": message,
            "removed_count": removed_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理客户端失败: {str(e)}")

@app.post("/api/clients/{client_id}/auto-setup")
async def auto_setup_client(client_id: str):
    """设置在线客户端的参数（使用客户端环境变量配置）"""
    try:
        # 检查客户端是否存在且在线
        client = client_manager.get_client(client_id)
        if not client:
            raise HTTPException(status_code=404, detail="客户端不存在")

        if client['status'] != 'online':
            raise HTTPException(status_code=400, detail="客户端不在线")

        # 调用客户端的自动设置API
        result = await call_client_auto_setup_api(client)

        if result.get('success'):
            return {
                "success": True,
                "message": f"客户端 {client_id} 参数设置成功",
                "data": {
                    "client_id": client_id,
                    "client_result": result.get('data', {}),
                    "message": result.get('message', '')
                }
            }
        else:
            raise HTTPException(status_code=500, detail=result.get('message', '参数设置失败'))

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"自动设置参数失败: {str(e)}")

@app.post("/api/clients/{client_id}/wait-and-setup")
async def wait_and_setup_client(client_id: str):
    """等待客户端上线并自动设置参数（使用客户端环境变量配置）"""
    try:
        # 检查客户端是否存在
        client = client_manager.get_client(client_id)
        if not client:
            raise HTTPException(status_code=404, detail="客户端不存在")

        # 启动监控任务
        start_client_monitoring(client_id)

        return {
            "success": True,
            "message": f"已开始监控客户端 {client_id}，上线后将使用客户端环境变量配置自动设置参数",
            "data": {
                "client_id": client_id,
                "note": "将使用客户端的环境变量配置进行参数设置"
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动客户端监控失败: {str(e)}")

@app.post("/api/clients/{client_id}/cancel-wait")
async def cancel_wait_client(client_id: str):
    """取消等待客户端上线的监控任务"""
    try:
        if client_id in monitoring_tasks:
            # 取消监控任务
            monitoring_tasks[client_id].cancel()
            del monitoring_tasks[client_id]

            return {
                "success": True,
                "message": f"已取消客户端 {client_id} 的等待监控"
            }
        else:
            return {
                "success": False,
                "message": f"客户端 {client_id} 没有正在进行的监控任务"
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消监控失败: {str(e)}")

@app.get("/api/monitoring-status")
async def get_monitoring_status():
    """获取当前监控状态"""
    try:
        # 清理已完成的任务
        completed_tasks = []
        for client_id, task in monitoring_tasks.items():
            if task.done():
                completed_tasks.append(client_id)

        for client_id in completed_tasks:
            logging.info(f"清理已完成的监控任务: {client_id}")
            del monitoring_tasks[client_id]

        # 构建状态信息
        status = {}
        for client_id, task in monitoring_tasks.items():
            status[client_id] = {
                "client_id": client_id,
                "is_running": not task.done(),
                "is_cancelled": task.cancelled()
            }

        return {
            "success": True,
            "data": {
                "active_monitoring_count": len([t for t in monitoring_tasks.values() if not t.done()]),
                "monitoring_tasks": status
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取监控状态失败: {str(e)}")

@app.get("/config.html")
async def config_page():
    """配置管理页面"""
    config_file = os.path.join(static_path, "config.html")
    if os.path.exists(config_file):
        return FileResponse(config_file)
    else:
        return JSONResponse(
            status_code=404,
            content={"success": False, "message": "配置页面不存在"}
        )

@app.get("/log-manager.html")
async def log_manager_page():
    """日志管理页面"""
    log_manager_file = os.path.join(static_path, "log-manager.html")
    if os.path.exists(log_manager_file):
        return FileResponse(log_manager_file)
    else:
        return JSONResponse(
            status_code=404,
            content={"success": False, "message": "日志管理页面不存在"}
        )

@app.get("/api/config")
async def get_config():
    """获取当前配置"""
    try:
        config_manager = global_config_manager or get_config_manager()
        config = config_manager.get_config()
        return {
            "success": True,
            "data": config
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")

@app.post("/api/config")
async def save_config(request: Request):
    """保存配置"""
    try:
        data = await request.json()
        config_manager = global_config_manager or get_config_manager()

        # 验证并保存配置
        result = config_manager.update_config(data)

        if result["success"]:
            return result
        else:
            return JSONResponse(
                status_code=400,
                content=result
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存配置失败: {str(e)}")

@app.post("/api/config/reset")
async def reset_config():
    """重置配置为默认值"""
    try:
        config_manager = global_config_manager or get_config_manager()
        success = config_manager.reset_to_default()

        if success:
            return {
                "success": True,
                "message": "配置已重置为默认值",
                "data": config_manager.get_config()
            }
        else:
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": "重置配置失败"}
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置配置失败: {str(e)}")

@app.get("/api/log-info")
async def get_log_info():
    """获取日志信息"""
    try:
        log_manager = global_log_manager or get_log_manager()
        log_info = log_manager.get_log_info()

        # 添加连接日志信息
        connection_log_file = "logs/client_connections.log"
        if os.path.exists(connection_log_file):
            connection_log_size = os.path.getsize(connection_log_file)
            log_info["connection_log"] = {
                "filename": "client_connections.log",
                "file_path": connection_log_file,
                "size_mb": connection_log_size / 1024 / 1024,
                "modified_time": datetime.fromtimestamp(os.path.getmtime(connection_log_file)).strftime("%Y-%m-%d %H:%M:%S")
            }

        # 获取所有日志文件列表
        log_dir = "logs"
        all_log_files = []
        if os.path.exists(log_dir):
            for filename in os.listdir(log_dir):
                if filename.endswith('.log'):
                    full_path = os.path.join(log_dir, filename)
                    if os.path.isfile(full_path):
                        all_log_files.append({
                            "filename": filename,
                            "size_mb": os.path.getsize(full_path) / 1024 / 1024,
                            "modified_time": datetime.fromtimestamp(os.path.getmtime(full_path)).strftime("%Y-%m-%d %H:%M:%S")
                        })

        # 按修改时间排序
        all_log_files.sort(key=lambda x: x["modified_time"], reverse=True)
        log_info["all_log_files"] = all_log_files

        return {
            "success": True,
            "data": log_info
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取日志信息失败: {str(e)}")

@app.post("/api/log-rotate")
async def rotate_log():
    """手动执行日志轮转"""
    try:
        log_manager = global_log_manager or get_log_manager()
        log_manager.force_rotate()

        return {
            "success": True,
            "message": "日志轮转执行成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行日志轮转失败: {str(e)}")

@app.post("/api/log-config")
async def update_log_config(request: Request):
    """更新日志配置"""
    try:
        data = await request.json()

        # 验证参数
        max_size_mb = data.get('max_size_mb')
        backup_count = data.get('backup_count')
        log_level = data.get('log_level')

        if max_size_mb is not None:
            if not isinstance(max_size_mb, (int, float)) or max_size_mb <= 0 or max_size_mb > 1000:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "message": "最大文件大小必须在0-1000MB之间"}
                )

        if backup_count is not None:
            if not isinstance(backup_count, int) or backup_count < 0 or backup_count > 100:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "message": "备份文件数量必须在0-100之间"}
                )

        if log_level is not None:
            valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if log_level not in valid_levels:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "message": f"日志级别必须是以下之一: {', '.join(valid_levels)}"}
                )

        # 更新配置
        log_manager = global_log_manager or get_log_manager()
        updated = log_manager.update_config(
            max_size_mb=max_size_mb,
            backup_count=backup_count,
            log_level=log_level
        )

        if updated:
            return {
                "success": True,
                "message": "日志配置更新成功",
                "data": log_manager.get_log_info()
            }
        else:
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": "日志配置更新失败"}
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新日志配置失败: {str(e)}")

@app.get("/api/log-content/{filename}")
async def get_log_content(filename: str, lines: int = 100):
    """获取日志文件内容"""
    try:
        # 安全检查：只允许读取logs目录下的.log文件
        if not filename.endswith('.log') or '/' in filename or '\\' in filename:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "无效的文件名"}
            )

        # 构建文件路径
        log_dir = "logs"
        file_path = os.path.join(log_dir, filename)

        if not os.path.exists(file_path):
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "文件不存在"}
            )

        # 读取文件内容（最后N行）
        content_lines = []
        file_size = os.path.getsize(file_path)

        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            all_lines = f.readlines()

            # 获取最后N行
            if lines > 0:
                content_lines = all_lines[-lines:]
            else:
                content_lines = all_lines

        return {
            "success": True,
            "data": {
                "filename": filename,
                "file_size": file_size,
                "total_lines": len(all_lines),
                "displayed_lines": len(content_lines),
                "content": ''.join(content_lines)
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取日志文件失败: {str(e)}")

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "客户端管理系统",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

# 错误处理
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"success": False, "message": "页面不存在"}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"success": False, "message": "服务器内部错误"}
    )

def set_global_config_manager(config_manager):
    """设置全局配置管理器引用"""
    global global_config_manager
    global_config_manager = config_manager

def set_global_log_manager(log_manager):
    """设置全局日志管理器引用"""
    global global_log_manager
    global_log_manager = log_manager

def set_global_client_manager(cm):
    """设置全局客户端管理器"""
    global client_manager
    client_manager = cm

# 客户端监控任务字典
monitoring_tasks = {}

async def cleanup_completed_monitoring_tasks():
    """定期清理已完成的监控任务"""
    while True:
        try:
            completed_tasks = []
            for client_id, task in monitoring_tasks.items():
                if task.done():
                    completed_tasks.append(client_id)

            for client_id in completed_tasks:
                logging.info(f"定期清理已完成的监控任务: {client_id}")
                del monitoring_tasks[client_id]

            if completed_tasks:
                logging.info(f"清理了 {len(completed_tasks)} 个已完成的监控任务")

        except Exception as e:
            logging.error(f"清理监控任务时发生错误: {e}")

        # 每30秒清理一次
        await asyncio.sleep(30)



async def call_client_auto_setup_api(client):
    """调用客户端的自动设置API"""
    try:
        client_url = f"http://{client['ip']}:{client['web_port']}"
        logging.info(f"调用客户端自动设置API: {client_url}/api/auto-setup")
        logging.info(f"客户端信息: IP={client['ip']}, Port={client['web_port']}, Status={client['status']}")

        # 设置超时时间
        timeout = aiohttp.ClientTimeout(total=30)  # 批量操作需要更长时间

        async with aiohttp.ClientSession(timeout=timeout) as session:
            # 首先测试客户端是否可访问
            try:
                logging.info(f"测试客户端连接: {client_url}/health")
                async with session.get(f"{client_url}/health") as health_response:
                    logging.info(f"客户端健康检查响应: {health_response.status}")
                    if health_response.status != 200:
                        return {
                            "success": False,
                            "message": f"客户端健康检查失败: HTTP {health_response.status}"
                        }
            except aiohttp.ClientConnectorError as e:
                logging.error(f"无法连接到客户端进行健康检查 {client_url}: {e}")
                return {
                    "success": False,
                    "message": f"无法连接到客户端 {client_url}: 连接被拒绝或客户端未启动"
                }
            except Exception as e:
                logging.error(f"健康检查异常: {e}")
                return {
                    "success": False,
                    "message": f"客户端连接测试失败: {str(e)}"
                }

            # 调用自动设置API
            try:
                async with session.post(f"{client_url}/api/auto-setup") as response:
                    logging.info(f"客户端API响应状态: {response.status}")

                    if response.status == 200:
                        data = await response.json()
                        logging.info(f"客户端API响应: {data.get('message', '')}")
                        return data
                    elif response.status == 404:
                        logging.error(f"客户端API不存在: {client_url}/api/auto-setup")
                        return {
                            "success": False,
                            "message": f"客户端不支持自动设置功能，请确保客户端版本支持此功能"
                        }
                    else:
                        logging.error(f"客户端API调用失败: HTTP {response.status}")
                        response_text = await response.text()
                        logging.error(f"响应内容: {response_text}")
                        return {
                            "success": False,
                            "message": f"客户端API返回错误状态: {response.status}"
                        }

            except aiohttp.ClientConnectorError as e:
                logging.error(f"无法连接到客户端API {client_url}: {e}")
                return {
                    "success": False,
                    "message": f"无法连接到客户端API: {str(e)}"
                }
            except asyncio.TimeoutError:
                logging.error(f"调用客户端API超时: {client_url}")
                return {
                    "success": False,
                    "message": "调用客户端API超时"
                }

    except Exception as e:
        logging.error(f"调用客户端API失败: {e}")
        return {
            "success": False,
            "message": f"调用客户端API失败: {str(e)}"
        }

def start_client_monitoring(client_id):
    """启动客户端监控任务"""
    # 如果已有监控任务，先取消
    if client_id in monitoring_tasks:
        old_task = monitoring_tasks[client_id]
        if not old_task.done():
            old_task.cancel()
        del monitoring_tasks[client_id]
        logging.info(f"取消客户端 {client_id} 的旧监控任务")

    # 创建新的监控任务
    task = asyncio.create_task(monitor_client_and_setup(client_id))
    monitoring_tasks[client_id] = task

    logging.info(f"已启动客户端 {client_id} 的监控任务")

async def monitor_client_and_setup(client_id):
    """监控客户端状态并在上线时自动设置参数（使用客户端环境变量配置）"""
    try:
        logging.info(f"开始监控客户端 {client_id}，等待上线后使用环境变量配置自动设置参数")

        check_interval = 5  # 每5秒检查一次
        retry_interval = 5  # 设置失败后5秒重试

        while True:
            # 检查任务是否被取消
            if client_id not in monitoring_tasks:
                logging.info(f"客户端 {client_id} 监控任务已被取消")
                return

            # 检查客户端状态
            client = client_manager.get_client(client_id)
            if client and client['status'] == 'online':
                logging.info(f"客户端 {client_id} 已上线，开始调用自动设置API")

                # 调用自动设置API（客户端将使用环境变量配置）
                result = await call_client_auto_setup_api(client)
                success = result.get('success', False)

                if success:
                    logging.info(f"客户端 {client_id} 自动设置成功，停止监控")
                    # 参数设置成功，自动取消等待
                    if client_id in monitoring_tasks:
                        del monitoring_tasks[client_id]

                    # 记录成功完成的监控任务，便于前端及时更新状态
                    logging.info(f"监控任务完成: 客户端 {client_id} 已上线并成功完成自动设置")
                    return
                else:
                    logging.error(f"客户端 {client_id} 自动设置失败: {result.get('message', '未知错误')}，{retry_interval}秒后重试")
                    # 设置失败，等待5秒后重试
                    await asyncio.sleep(retry_interval)
                    continue

            # 等待下次检查
            await asyncio.sleep(check_interval)

    except asyncio.CancelledError:
        logging.info(f"客户端 {client_id} 监控任务被取消")
        if client_id in monitoring_tasks:
            del monitoring_tasks[client_id]
    except Exception as e:
        logging.error(f"监控客户端 {client_id} 时发生错误: {e}")
        if client_id in monitoring_tasks:
            del monitoring_tasks[client_id]

async def start_web_server():
    """启动Web服务器"""
    config_manager = global_config_manager or get_config_manager()
    config = config_manager.get_config()

    port = int(config.get('WEB_PORT', '8000'))
    log_level = config.get('LOG_LEVEL', 'info').lower()

    # 配置日志
    logger = logging.getLogger(__name__)
    logger.info(f"Web服务器配置 - 端口: {port}, 日志级别: {log_level}")

    # 启动定期清理任务
    cleanup_task = asyncio.create_task(cleanup_completed_monitoring_tasks())
    logger.info("已启动监控任务清理服务")

    config = uvicorn.Config(
        app,
        host="0.0.0.0",
        port=port,
        log_level=log_level,
        access_log=True
    )
    server = uvicorn.Server(config)
    logger.info(f"Web服务器启动，访问 http://localhost:{port}")

    try:
        await server.serve()
    finally:
        # 清理任务
        cleanup_task.cancel()
        try:
            await cleanup_task
        except asyncio.CancelledError:
            pass

if __name__ == "__main__":
    import asyncio
    asyncio.run(start_web_server())
